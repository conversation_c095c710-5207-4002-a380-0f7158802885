import "./App.css";
import { useState } from "react";

const App = () => {
  const [adminUser, setAdminUser] = useState({
    name: "ahmed",
    age: 24,
  });
  const [isAdmin, setIsAdmin] = useState(true);
  const changeName = (newName) => {
    setAdminUser({
      ...adminUser,
      name: newName,
    });
  };
  return (
    <>
      <h2>Hello function component</h2>
      <h3>
        hello, {adminUser.name} has {adminUser.age}
      </h3>
      <button
        onClick={() => {
          changeName("abdo");
        }}
      >
        Change name
      </button>
    </>
  );
};

export default App;
